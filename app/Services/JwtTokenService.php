<?php

namespace App\Services;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Log;

class JwtTokenService
{
    /**
     * Create a short-lived JWT token for deep link authentication.
     */
    public function createShortLivedToken(array $userData, int $expirySeconds = 60): string
    {
        $now = time();
        $payload = [
            'sub' => $userData['sub'],
            'email' => $userData['email'],
            'name' => $userData['name'],
            'exp' => $now + $expirySeconds,
            'iat' => $now,
        ];

        Log::info('JWT payload created', [
            'payload' => $payload,
            'expires_at' => date('Y-m-d H:i:s', $payload['exp']),
            'issued_at' => date('Y-m-d H:i:s', $payload['iat']),
        ]);

        $key = $this->getTokenSigningKey();
        $token = JWT::encode($payload, $key, 'HS256');

        Log::info('JWT token encoded', [
            'key_length' => strlen($key),
            'algorithm' => 'HS256',
        ]);

        return $token;
    }

    /**
     * Decode and validate a JWT token.
     */
    public function decodeToken(string $jwtToken): object
    {
        $key = $this->getTokenSigningKey();
        $decoded = JWT::decode($jwtToken, new Key($key, 'HS256'));

        Log::info('JWT token decoded', [
            'sub' => $decoded->sub ?? 'missing',
            'email' => $decoded->email ?? 'missing',
            'exp' => $decoded->exp ?? 'missing',
            'current_time' => time(),
        ]);

        return $decoded;
    }

    /**
     * Validate if a JWT token is not expired.
     */
    public function isTokenValid(object $decodedToken): bool
    {
        if (!isset($decodedToken->exp)) {
            return false;
        }

        $isValid = $decodedToken->exp >= time();
        
        if (!$isValid) {
            Log::warning('Expired JWT token detected', [
                'exp' => $decodedToken->exp,
                'current_time' => time(),
            ]);
        }

        return $isValid;
    }

    /**
     * Decode and validate a JWT token in one step.
     */
    public function decodeAndValidateToken(string $jwtToken): ?object
    {
        try {
            $decoded = $this->decodeToken($jwtToken);
            
            if (!$this->isTokenValid($decoded)) {
                return null;
            }

            return $decoded;
        } catch (\Exception $e) {
            Log::warning('JWT token validation failed', [
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Resolve and validate the token signing key from configuration.
     * Requires a base64-encoded key of at least 32 bytes after decoding.
     */
    private function getTokenSigningKey(): string
    {
        $configured = (string) config('app.token_signing_key', '');
        if ($configured === '') {
            throw new \RuntimeException('Token signing key is not configured');
        }

        // Allow Laravel-style prefix "base64:" and raw base64 strings
        $normalized = str_starts_with($configured, 'base64:') ? substr($configured, 7) : $configured;

        $decoded = base64_decode($normalized, true);
        if ($decoded === false) {
            throw new \RuntimeException('Token signing key must be valid base64');
        }

        if (strlen($decoded) < 32) { // 256-bit minimum
            throw new \RuntimeException('Token signing key is too short; require >= 32 bytes');
        }

        return $decoded;
    }

    /**
     * Create a JWT token with custom payload and expiry.
     */
    public function createToken(array $payload, int $expirySeconds = 3600): string
    {
        $now = time();
        $payload['iat'] = $payload['iat'] ?? $now;
        $payload['exp'] = $payload['exp'] ?? ($now + $expirySeconds);

        $key = $this->getTokenSigningKey();
        return JWT::encode($payload, $key, 'HS256');
    }

    /**
     * Extract user data from a decoded JWT token.
     */
    public function extractUserData(object $decodedToken): array
    {
        return [
            'sub' => $decodedToken->sub ?? null,
            'email' => $decodedToken->email ?? null,
            'name' => $decodedToken->name ?? null,
        ];
    }
}
