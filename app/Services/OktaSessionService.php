<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserSession;
use Illuminate\Support\Facades\Log;

class OktaSessionService
{
    /**
     * Find an active user session by token hash.
     */
    public function findActiveSessionByToken(User $user, string $tokenHash): ?UserSession
    {
        return UserSession::where('user_id', $user->id)
            ->where('app_token_hash', $tokenHash)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Find the latest active session for a user.
     */
    public function findActiveSession(User $user): ?UserSession
    {
        return UserSession::where('user_id', $user->id)
            ->where('is_active', true)
            ->latest()
            ->first();
    }

    /**
     * Create a new user session with encrypted Okta tokens.
     */
    public function createUserSession(User $user, array $tokens, array $oktaProfile, string $platform = 'mobile'): UserSession
    {
        return UserSession::create([
            'user_id' => $user->id,
            // Pass raw tokens; Model mutators handle encryption at rest
            'okta_access_token' => $tokens['access_token'] ?? null,
            'okta_refresh_token' => $tokens['refresh_token'] ?? null,
            'okta_id_token' => $tokens['id_token'] ?? null,
            'okta_expires_at' => now()->addSeconds($tokens['expires_in'] ?? 3600),
            'platform' => $platform,
            'okta_user_data' => $oktaProfile,
            'is_active' => true,
            'last_activity_at' => now(),
        ]);
    }

    /**
     * Associate an app token hash with the latest active session.
     */
    public function associateTokenWithSession(User $user, string $tokenHash): bool
    {
        try {
            $session = $this->findActiveSession($user);
            if ($session) {
                $session->app_token_hash = $tokenHash;
                $session->save();
                return true;
            }
            return false;
        } catch (\Exception $e) {
            Log::warning('Failed to persist app_token_hash', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Check if a user has a valid Okta session.
     */
    public function isOktaSessionValid(User $user): bool
    {
        $session = $this->findActiveSession($user);

        if (!$session || !$session->isOktaSessionValid()) {
            return false;
        }

        // Update activity timestamp
        $session->updateActivity();

        return true;
    }

    /**
     * Deactivate a user session and revoke associated Okta tokens.
     */
    public function deactivateSession(UserSession $userSession, OktaService $oktaService): void
    {
        // Revoke Okta tokens using decrypted accessors on the model
        if ($userSession->okta_access_token) {
            $oktaService->revokeToken($userSession->okta_access_token, 'access_token');
        }
        if ($userSession->okta_refresh_token) {
            $oktaService->revokeToken($userSession->okta_refresh_token, 'refresh_token');
        }

        // Deactivate the session
        $userSession->deactivate();
    }

    /**
     * Logout a user by finding their session via token and deactivating it.
     */
    public function logoutUserByToken(User $user, string $tokenHash, OktaService $oktaService): bool
    {
        $userSession = $this->findActiveSessionByToken($user, $tokenHash);

        if ($userSession) {
            $this->deactivateSession($userSession, $oktaService);
            return true;
        }

        return false;
    }
}
