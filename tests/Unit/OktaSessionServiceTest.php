<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\UserSession;
use App\Services\OktaService;
use App\Services\OktaSessionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

class OktaSessionServiceTest extends TestCase
{
    use RefreshDatabase;

    private OktaSessionService $oktaSessionService;
    private OktaService $oktaService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->oktaSessionService = new OktaSessionService();
        $this->oktaService = Mockery::mock(OktaService::class);
    }

    public function test_find_active_session_by_token_returns_correct_session()
    {
        $user = User::factory()->create();
        $tokenHash = 'test-token-hash';

        // Create an active session with the token hash
        $activeSession = UserSession::factory()->create([
            'user_id' => $user->id,
            'app_token_hash' => $tokenHash,
            'is_active' => true,
        ]);

        // Create an inactive session with the same token hash (should not be returned)
        UserSession::factory()->create([
            'user_id' => $user->id,
            'app_token_hash' => $tokenHash,
            'is_active' => false,
        ]);

        $result = $this->oktaSessionService->findActiveSessionByToken($user, $tokenHash);

        $this->assertNotNull($result);
        $this->assertEquals($activeSession->id, $result->id);
        $this->assertTrue($result->is_active);
    }

    public function test_find_active_session_by_token_returns_null_when_no_match()
    {
        $user = User::factory()->create();
        $tokenHash = 'non-existent-token-hash';

        $result = $this->oktaSessionService->findActiveSessionByToken($user, $tokenHash);

        $this->assertNull($result);
    }

    public function test_find_active_session_returns_latest_active_session()
    {
        $user = User::factory()->create();

        // Create older active session
        $olderSession = UserSession::factory()->create([
            'user_id' => $user->id,
            'is_active' => true,
            'created_at' => now()->subHour(),
        ]);

        // Create newer active session
        $newerSession = UserSession::factory()->create([
            'user_id' => $user->id,
            'is_active' => true,
            'created_at' => now(),
        ]);

        // Create inactive session (should not be returned)
        UserSession::factory()->create([
            'user_id' => $user->id,
            'is_active' => false,
            'created_at' => now()->addMinute(),
        ]);

        $result = $this->oktaSessionService->findActiveSession($user);

        $this->assertNotNull($result);
        $this->assertEquals($newerSession->id, $result->id);
    }

    public function test_create_user_session_creates_session_with_correct_data()
    {
        $user = User::factory()->create();
        $tokens = [
            'access_token' => 'test-access-token',
            'refresh_token' => 'test-refresh-token',
            'id_token' => 'test-id-token',
            'expires_in' => 3600,
        ];
        $oktaProfile = ['sub' => 'test-sub', 'email' => '<EMAIL>'];
        $platform = 'mobile';

        $result = $this->oktaSessionService->createUserSession($user, $tokens, $oktaProfile, $platform);

        $this->assertInstanceOf(UserSession::class, $result);
        $this->assertEquals($user->id, $result->user_id);
        $this->assertEquals($platform, $result->platform);
        $this->assertTrue($result->is_active);
        $this->assertEquals($oktaProfile, $result->okta_user_data);

        // Test that tokens are encrypted (they should not match the plain text)
        $this->assertNotEquals($tokens['access_token'], $result->getAttributes()['okta_access_token']);

        // Test that decrypted tokens match
        $this->assertEquals($tokens['access_token'], $result->okta_access_token);
    }

    public function test_associate_token_with_session_updates_existing_session()
    {
        $user = User::factory()->create();
        $tokenHash = 'new-token-hash';

        $session = UserSession::factory()->create([
            'user_id' => $user->id,
            'is_active' => true,
            'app_token_hash' => null,
        ]);

        $result = $this->oktaSessionService->associateTokenWithSession($user, $tokenHash);

        $this->assertTrue($result);
        $session->refresh();
        $this->assertEquals($tokenHash, $session->app_token_hash);
    }

    public function test_associate_token_with_session_returns_false_when_no_active_session()
    {
        $user = User::factory()->create();
        $tokenHash = 'test-token-hash';

        // Create only inactive sessions
        UserSession::factory()->create([
            'user_id' => $user->id,
            'is_active' => false,
        ]);

        $result = $this->oktaSessionService->associateTokenWithSession($user, $tokenHash);

        $this->assertFalse($result);
    }

    public function test_is_okta_session_valid_returns_true_for_valid_session()
    {
        $user = User::factory()->create();

        $session = UserSession::factory()->create([
            'user_id' => $user->id,
            'is_active' => true,
            'okta_expires_at' => now()->addHour(),
        ]);

        $result = $this->oktaSessionService->isOktaSessionValid($user);

        $this->assertTrue($result);

        // Verify activity was updated
        $session->refresh();
        $this->assertNotNull($session->last_activity_at);
    }

    public function test_is_okta_session_valid_returns_false_for_expired_session()
    {
        $user = User::factory()->create();

        UserSession::factory()->create([
            'user_id' => $user->id,
            'is_active' => true,
            'okta_expires_at' => now()->subHour(), // Expired
        ]);

        $result = $this->oktaSessionService->isOktaSessionValid($user);

        $this->assertFalse($result);
    }

    public function test_deactivate_session_revokes_tokens_and_deactivates()
    {
        $userSession = UserSession::factory()->create([
            'is_active' => true,
            'okta_access_token' => 'test-access-token', // Let the model handle encryption
            'okta_refresh_token' => 'test-refresh-token', // Let the model handle encryption
        ]);

        $this->oktaService->shouldReceive('revokeToken')
            ->with('test-access-token', 'access_token')
            ->once();

        $this->oktaService->shouldReceive('revokeToken')
            ->with('test-refresh-token', 'refresh_token')
            ->once();

        $this->oktaSessionService->deactivateSession($userSession, $this->oktaService);

        $userSession->refresh();
        $this->assertFalse($userSession->is_active);
    }

    public function test_logout_user_by_token_successfully_logs_out_user()
    {
        $user = User::factory()->create();
        $tokenHash = 'test-token-hash';

        $userSession = UserSession::factory()->create([
            'user_id' => $user->id,
            'app_token_hash' => $tokenHash,
            'is_active' => true,
            'okta_access_token' => 'test-access-token', // Let the model handle encryption
            'okta_refresh_token' => 'test-refresh-token', // Let the model handle encryption
        ]);

        $this->oktaService->shouldReceive('revokeToken')
            ->with('test-access-token', 'access_token')
            ->once();

        $this->oktaService->shouldReceive('revokeToken')
            ->with('test-refresh-token', 'refresh_token')
            ->once();

        $result = $this->oktaSessionService->logoutUserByToken($user, $tokenHash, $this->oktaService);

        $this->assertTrue($result);
        $userSession->refresh();
        $this->assertFalse($userSession->is_active);
    }

    public function test_logout_user_by_token_returns_false_when_no_session_found()
    {
        $user = User::factory()->create();
        $tokenHash = 'non-existent-token-hash';

        $result = $this->oktaSessionService->logoutUserByToken($user, $tokenHash, $this->oktaService);

        $this->assertFalse($result);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
